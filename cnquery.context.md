# CNQuery Repository Context

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

cnquery is a cloud-native infrastructure query tool built around MQL (Mondoo Query Language). It uses a modular, plugin-based architecture that enables querying different types of infrastructure through a unified query language.

## Common Development Commands

### Build and Development
- **`make prep`** - Install required tools (protobuf, linting tools, etc.)
- **`make cnquery/generate`** - Generate all required files after making changes to resources, providers, or protos
- **`make providers`** - Build all providers (equivalent to `make providers/build`)
- **`make cnquery/install`** - Install cnquery binary to $GOBIN
- **`make cnquery/build`** - Build cnquery binary locally

### Testing and Quality
- **`make test`** - Run tests and linting (`make test/go test/lint`)
- **`make test/go`** - Run Go tests (excludes provider tests)
- **`make test/lint`** - Run golangci-lint
- **`make providers/test`** - Run all provider tests
- **`make race/go`** - Run race condition tests for critical components

### Provider Development
- **`make providers/lr`** - Build the lr tool for processing .lr resource files
- **`make providers/config`** - Generate provider configuration (needed when modifying providers.yaml)
- **`make providers/build/PROVIDER_NAME`** - Build specific provider (e.g., `make providers/build/aws`)
- **`make providers/install/PROVIDER_NAME`** - Install specific provider locally

### Single Test Execution
- **`go test ./path/to/package`** - Run tests for a specific package
- **`go test ./providers/PROVIDER_NAME/...`** - Run all tests for a specific provider
- **`gotestsum --format pkgname -- ./...`** - Run tests with better output formatting

## High-Level Architecture

### Core Components
- **`mqlc/`** - MQL compiler that transforms MQL source into executable bytecode
- **`mql/`** - High-level MQL runtime interface and execution
- **`llx/`** - Low-level execution engine that runs compiled MQL bytecode
- **`explorer/`** - Query execution orchestration and job management
- **`providers/`** - Plugin system for different infrastructure providers

### Provider Architecture
- Each provider is a separate binary communicating via gRPC
- Providers implement resources defined in `.lr` files
- The coordinator manages provider lifecycle and connections
- Resources represent infrastructure entities with queryable fields

### MQL Execution Flow
1. MQL source → Parser → AST → Compiler → CodeBundle
2. CodeBundle → llx.MQLExecutorV2 → Resource calls → Provider gRPC
3. Provider responses → Result aggregation → Output formatting

### Key Data Structures
- **`llx.CodeBundle`** - Compiled MQL bytecode ready for execution
- **`*mql*` structs** - Generated resource implementations from .lr files
- **`plugin.Runtime`** - Execution context linking assets to providers
- **`explorer.Explorer`** - High-level query orchestration

## Development Workflow

### Debugging Providers Locally
1. Edit `providers.yaml` to add provider to `builtin: []` list
2. Run `make providers/config` to regenerate configuration
3. Use `apps/cnquery/cnquery.go` as main entry point in debugger
4. Restore `providers.yaml` when done and re-run `make providers/config`

### Adding New Resources
1. Define resources in `providers/PROVIDER/resources/PROVIDER.lr`
2. Run `make cnquery/generate` to generate Go structs and interfaces
3. Implement resource methods in `providers/PROVIDER/resources/` Go files
4. Update provider version when ready to release

### Provider Development Best Practices
- Use resource init functions for cross-referencing to leverage MQL caching
- Filter resources in memory when possible to reduce API calls
- Implement lazy evaluation for expensive operations
- Follow existing patterns for error handling and resource creation

## Step-by-Step: Creating Resources for Providers

### Quick Development Cycle
When developing resources for a specific provider, build and test incrementally:

```bash
# Build, install, and test a specific provider
make providers/build/aws && make providers/install/aws && cnspec shell aws --verbose
```

Use `--verbose` flag to see debug logs during development.

### Debugging with Debugger Tools
For deeper debugging using `dlv` or similar tools:

1. **Prepare for debugging**: Edit `providers.yaml` to add provider to `builtin: []` list, then run `make providers/config`
2. **Debug with dlv**:
   ```bash
   dlv debug ./apps/cnquery/cnquery.go --listen=:12345 -- run aws shell -c 'aws.rds { * }' --verbose
   ```
3. **Restore configuration**: Restore `providers.yaml` and re-run `make providers/config` when done

Note: Debug statements are often more practical than debugger tools for provider development.

### Creating a New MQL Resource: Complete Walkthrough

#### Step 1: Research and Planning
Before coding, understand the resource you're implementing:
- Study the official API documentation
- Test the API using CLI tools to understand request/response patterns
- Identify the data structure and relationships
- Plan the MQL query structure you want to achieve

Example goal: Add RDS Parameter Groups queryable via `aws.rds { parameterGroups }`

#### Step 2: Define Resource Structure in .lr File
Create the resource definition in `providers/PROVIDER/resources/PROVIDER.lr`:

```go
// Amazon RDS parameter groups
aws.rds.parameterGroup @defaults("name family region arn") {
  // ARN for resource
  arn string
  // Family of the Parameter Group
  family string
  // Name of the Parameter Group
  name string
  // Description of the Parameter Group
  description string
  // Region of the parameters
  region string
}
```

**Key points:**
- `@defaults("...")` specifies what fields show by default in console output
- Include `region` for multi-region resources to avoid duplicates
- Use descriptive comments for each field

#### Step 3: Generate Code Structure
Run the code generation to create Go structs:
```bash
make providers/build/aws
```

This generates the resource definitions in `aws.lr.go` and manifest in `aws.lr.manifest.yaml`.

#### Step 4: Connect Resource to Parent
Add the new resource to its parent resource in the `.lr` file:

```go
// Amazon Relational Database Service (RDS)
aws.rds {
  // ... existing resources ...
  // List of all parameter groups
  parameterGroups() []aws.rds.parameterGroup
}
```

#### Step 5: Implement Resource Collection Function
Create the main collection function following the established pattern:

```go
func (a *mqlAwsRds) parameterGroups() ([]interface{}, error) {
  conn := a.MqlRuntime.Connection.(*connection.AwsConnection)
  res := []interface{}{}
  
  // Use job pool for concurrent region processing
  poolOfJobs := jobpool.CreatePool(a.getParameterGroups(conn), 5)
  poolOfJobs.Run()
  
  // Check for errors
  if poolOfJobs.HasErrors() {
    return nil, poolOfJobs.GetErrors()
  }
  
  // Collect all results
  for i := range poolOfJobs.Jobs {
    res = append(res, poolOfJobs.Jobs[i].Result.([]interface{})...)
  }
  return res, nil
}
```

#### Step 6: Implement Region-Specific Data Fetching
Create the function that fetches data from each region:

```go
func (a *mqlAwsRds) getParameterGroups(conn *connection.AwsConnection) []*jobpool.Job {
  tasks := make([]*jobpool.Job, 0)
  regions, err := conn.Regions()
  if err != nil {
    return []*jobpool.Job{{Err: err}}
  }
  
  for _, region := range regions {
    regionVal := region
    f := func() (jobpool.JobResult, error) {
      log.Debug().Msgf("rds>getParameterGroup>calling aws with region %s", regionVal)
      res := []interface{}{}
      svc := conn.Rds(regionVal)
      ctx := context.Background()

      var marker *string
      for {
        // Call AWS API
        DBParameterGroups, err := svc.DescribeDBParameterGroups(ctx, &rds.DescribeDBParameterGroupsInput{Marker: marker})
        if err != nil {
          if Is400AccessDeniedError(err) {
            log.Warn().Str("region", regionVal).Msg("error accessing region for AWS API")
            return res, nil
          }
          return nil, err
        }
        
        // Process each resource
        for _, dbParameterGroup := range DBParameterGroups.DBParameterGroups {
          mqlParameterGroup, err := newMqlAwsParameterGroup(a.MqlRuntime, region, dbParameterGroup)
          if err != nil {
            return nil, err
          }
          res = append(res, mqlParameterGroup)
        }
        
        // Handle pagination
        if marker == nil {
          break
        }
        marker = DBParameterGroups.Marker
      }
      return jobpool.JobResult(res), nil
    }
    tasks = append(tasks, jobpool.NewJob(f))
  }
  return tasks
}
```

#### Step 7: Implement Resource Constructor
Create the function that builds individual resource instances:

```go
func newMqlAwsParameterGroup(runtime *plugin.Runtime, region string, parameterGroup rds_types.DBParameterGroup) (*mqlAwsRdsParameterGroup, error) {
  resource, err := CreateResource(runtime, "aws.rds.parameterGroup",
    map[string]*llx.RawData{
      "__id":        llx.StringData(fmt.Sprintf("%s/%s", *parameterGroup.DBParameterGroupArn, *parameterGroup.DBParameterGroupName)),
      "arn":         llx.StringDataPtr(parameterGroup.DBParameterGroupArn),
      "family":      llx.StringDataPtr(parameterGroup.DBParameterGroupFamily),
      "name":        llx.StringDataPtr(parameterGroup.DBParameterGroupName),
      "description": llx.StringDataPtr(parameterGroup.Description),
      "region":      llx.StringData(region),
    })
  if err != nil {
    return nil, err
  }
  return resource.(*mqlAwsRdsParameterGroup), nil
}
```

**Critical**: The `__id` field must be unique across all instances to prevent resource collision.

#### Step 8: Test Basic Implementation
Build and test the basic resource:
```bash
make providers/build/aws && make providers/install/aws && cnspec shell aws --verbose
```

Test with: `aws.rds { parameterGroups }`

#### Step 9: Add Child Resources (If Needed)
For resources with sub-resources (like parameters within parameter groups):

1. **Define child resource** in `.lr` file:
```go
aws.rds.parameterGroup.parameter @defaults("name value") {
  // Parameter fields...
}
```

2. **Add function to parent** resource:
```go
aws.rds.parameterGroup @defaults("name family region arn") {
  // ... existing fields ...
  // The parameters of the group
  parameters() []aws.rds.parameterGroup.parameter
}
```

3. **Implement child collection function**:
```go
func (a *mqlAwsRdsParameterGroup) parameters() ([]interface{}, error) {
  conn := a.MqlRuntime.Connection.(*connection.AwsConnection)
  res := []interface{}{}
  svc := conn.Rds(a.Region.Data)
  ctx := context.Background()

  var marker *string
  for {
    parameters, err := svc.DescribeDBParameters(ctx, &rds.DescribeDBParametersInput{
      DBParameterGroupName: &a.Name.Data,
      Marker:               marker,
    })
    if err != nil {
      return nil, err
    }
    
    for _, parameter := range parameters.Parameters {
      mqlParameter, err := newMqlAwsRdsParameterGroupParameter(a.MqlRuntime, parameter)
      if err != nil {
        return nil, err
      }
      res = append(res, mqlParameter)
    }
    
    if parameters.Marker == nil {
      break
    }
    marker = parameters.Marker
  }
  return res, nil
}
```

### Development Tips and Best Practices

#### Error Handling Patterns
- Always handle AWS access denied errors gracefully with `Is400AccessDeniedError(err)`
- Log region-specific warnings when access is denied
- Use proper context for API calls

#### Performance Considerations
- Use job pools for concurrent region processing
- Implement pagination correctly for large result sets
- Cache expensive operations when possible

#### Resource Design Patterns
- Always include region information for multi-region resources
- Use descriptive `__id` values that guarantee uniqueness
- Follow naming conventions: use descriptive field names and comments
- Group related functionality logically

#### Testing Your Implementation
1. Test basic resource listing: `aws.rds { parameterGroups }`
2. Test field access: `aws.rds { parameterGroups { name family } }`
3. Test child resources: `aws.rds { parameterGroups[0].parameters }`
4. Test across multiple regions if applicable
5. Test error conditions (invalid regions, access denied, etc.)

## Important Files and Directories

### Configuration
- **`providers.yaml`** - Provider registry and configuration
- **`go.mod`** - Go module with version pinning for stability
- **`Makefile`** - Build system with extensive provider automation

### Generated Code
- **`providers/builtin_dev.go`** - Auto-generated provider registry (do not edit)
- **`**/*.lr.go`** - Generated resource definitions from .lr files
- **`**/*.pb.go`** - Generated protobuf code

### CLI and User Interface
- **`apps/cnquery/cmd/`** - CLI command implementations
- **`cli/shell/`** - Interactive shell with auto-completion
- **`cli/reporter/`** - Output formatting (JSON, CSV, etc.)

## File Processing Guidelines

### Resource Definition Files (.lr)
- Located in `providers/*/resources/*.lr`
- Define MQL resources, fields, and relationships
- Processed by lr tool to generate Go code
- YAML manifests are auto-generated for documentation

### Provider Structure
- Each provider has: `config/`, `connection/`, `provider/`, `resources/`
- `main.go` is the provider binary entry point
- `gen/main.go` generates CLI configuration JSON

### Protocol Buffers
- Run `make cnquery/generate` after modifying .proto files
- Generated .pb.go files should not be manually edited
- Used for provider communication and data serialization

## Testing Guidelines

### Provider Testing
- Each provider should have comprehensive resource tests
- Use `providers-sdk/v1/testutils` for mock providers
- Test resource creation, field access, and error conditions
- Integration tests should validate actual provider communication

### Mock vs Real Testing
- Mock providers for unit testing individual resources
- Real providers for integration testing full workflows
- Recording/replay system available for reproducible tests

## MS365 Provider Resource Development Guide

This section provides a detailed walkthrough of adding resources to the MS365 provider, using the `transportConfig` resource implementation as a real example.

### MS365 Provider Architecture Overview

The MS365 provider integrates with Microsoft 365 services through PowerShell cmdlets and Microsoft Graph API. Key characteristics:

- **PowerShell Integration**: Exchange Online, SharePoint Online, and Teams use PowerShell modules
- **Graph API**: Core Microsoft services use REST API calls
- **Authentication**: Certificate-based authentication for production, interactive for development
- **Resource Structure**: Resources follow service boundaries (exchangeonline, sharepointonline, teams, etc.)

### Step-by-Step: Adding Exchange Online Resources

This example demonstrates adding the `ms365.exchangeonline.transportConfig` resource to expose Exchange transport settings.

#### Step 1: Research the Data Source

1. **Identify the PowerShell cmdlet**: Research the Exchange Online PowerShell documentation
   - Found: `Get-TransportConfig` cmdlet
   - Documentation: https://learn.microsoft.com/en-us/powershell/module/exchange/get-transportconfig
   - Key field: `SmtpClientAuthenticationDisabled` (boolean)

2. **Test the cmdlet manually** (if possible):
   ```powershell
   Connect-ExchangeOnline
   Get-TransportConfig | Select-Object SmtpClientAuthenticationDisabled
   ```

#### Step 2: Examine Existing MS365 Resources

Study existing implementations to understand patterns:

```bash
# Look at existing Exchange Online resources
cat providers/ms365/resources/ms365.lr | grep -A 10 -B 2 "ms365.exchangeonline"

# Examine the PowerShell script structure  
cat providers/ms365/resources/ms365_exchange.go | grep -A 20 "exchangeReport ="
```

Key observations:
- PowerShell scripts are embedded as Go string literals
- Single script fetches multiple resource types for efficiency
- Results are JSON-serialized and parsed into Go structs
- Resources can return either structured objects or dictionaries

#### Step 3: Add PowerShell Command to Script

Edit `providers/ms365/resources/ms365_exchange.go` and locate the `exchangeReport` variable:

```go
var exchangeReport = `
$appId = '%s'
$organization = '%s'  
$tenantId = '%s'
$outlookToken= '%s'

Install-Module -Name ExchangeOnlineManagement -Scope CurrentUser -Force
Import-Module ExchangeOnlineManagement
Connect-ExchangeOnline -AccessToken $outlookToken -AppID $appId -Organization $organization -ShowBanner:$false -ShowProgress:$false

// ... existing commands ...
$TransportConfig = (Get-TransportConfig)  // ADD THIS LINE

$exchangeOnline = New-Object PSObject
// ... existing Add-Member statements ...
Add-Member -InputObject $exchangeOnline -MemberType NoteProperty -Name TransportConfig -Value $TransportConfig  // ADD THIS LINE

Disconnect-ExchangeOnline -Confirm:$false
ConvertTo-Json -Depth 4 $exchangeOnline
`
```

#### Step 4: Update Go Struct for JSON Parsing

Add the field to the `ExchangeOnlineReport` struct:

```go
type ExchangeOnlineReport struct {
    MalwareFilterPolicy            []interface{}     `json:"MalwareFilterPolicy"`
    // ... existing fields ...
    TransportConfig                interface{}       `json:"TransportConfig"`  // ADD THIS LINE
    Mailbox                        []MailboxWithAudit `json:"Mailbox"`
}
```

#### Step 5: Define Resource in .lr File

Add the resource definition to `providers/ms365/resources/ms365.lr`:

```go
// Microsoft 365 Exchange Online
ms365.exchangeonline {
  // ... existing resources ...
  // Transport configuration settings
  transportConfig() dict
}
```

**Key Decision**: Use `dict` return type rather than structured resource for simpler implementation when you have complex nested data.

#### Step 6: Implement Resource Processing

Add processing logic in the `getExchangeReport()` function:

```go
func (r *mqlMs365Exchangeonline) getExchangeReport() error {
    // ... existing code ...
    
    // Process parsed JSON report
    // ... existing processing ...
    
    // Add transport config processing
    transportConfig, transportConfigErr := convert.JsonToDict(report.TransportConfig)
    r.TransportConfig = plugin.TValue[interface{}]{Data: transportConfig, State: plugin.StateIsSet, Error: transportConfigErr}
    
    return nil
}
```

Add the resource accessor method:

```go
func (r *mqlMs365Exchangeonline) transportConfig() (interface{}, error) {
    return nil, r.getExchangeReport()
}
```

#### Step 7: Build and Test

```bash
# Build the provider
make providers/build/ms365

# Install locally  
make providers/install/ms365

# Test the resource
cnspec run ms365 --tenant-id <tenant> --client-id <client> --certificate-path <cert> \
  -c 'ms365.exchangeonline.transportConfig["SmtpClientAuthenticationDisabled"] == true'
```

#### Step 8: Validate Results

Expected output for successful implementation:
```
[ok] value: true
```

Common issues and solutions:
- **"cannot find field 'transportConfig'"**: Resource not properly defined in .lr file
- **"cannot convert primitive with NO type information"**: Field name casing mismatch between JSON and resource definition
- **Hanging/timeout**: PowerShell command not executing properly or authentication issues

### MS365-Specific Development Patterns

#### Authentication Scopes
Different services require different OAuth scopes:
```go
const outlookScope = "https://outlook.office.com/.default"          // Exchange Online
const sharepointScope = "https://TENANT.sharepoint.com/.default"    // SharePoint Online  
const teamsScope = "48ac35b8-9aa8-4d74-927d-1f4a14a0b239/.default"  // Teams
```

#### PowerShell Module Management
Each service uses specific PowerShell modules:
- **Exchange Online**: `ExchangeOnlineManagement`
- **SharePoint Online**: `PnP.PowerShell`  
- **Teams**: `MicrosoftTeams`

#### Single-Fetch Pattern
MS365 resources use a single fetch approach per service to minimize authentication overhead:

```go
type mqlMs365ExchangeonlineInternal struct {
    exchangeReportLock sync.Mutex
    fetched            bool
    fetchErr           error
    org                string
}

func (r *mqlMs365Exchangeonline) getExchangeReport() error {
    r.exchangeReportLock.Lock()
    defer r.exchangeReportLock.Unlock()
    
    // Only fetch once
    if r.fetched {
        return r.fetchErr
    }
    // ... fetch logic ...
}
```

#### Error Handling for Permissions
Always handle authentication gracefully:

```go
if strings.Contains(strings.ToLower(str), "unauthorized") {
    return errHandler(errors.New("access denied, please ensure the credentials have the right permissions in Azure AD"))
}
```

#### Data Structure Decisions

**Use `dict` when**:
- Complex nested data structures from PowerShell
- Many fields that don't need individual type enforcement
- Quick implementation for exposing raw API data

**Use structured resources when**:
- Well-defined, stable data structures
- Type safety is important
- Need to implement computed fields or cross-references

### Testing MS365 Resources

#### Prerequisites
- Azure AD app registration with appropriate permissions
- Certificate-based authentication configured
- Access to a Microsoft 365 tenant

#### Test Commands
```bash
# Test basic connectivity
cnspec shell ms365 --tenant-id <id> --client-id <id> --certificate-path <path>

# Test specific resource  
cnspec run ms365 --tenant-id <id> --client-id <id> --certificate-path <path> \
  -c 'ms365.exchangeonline.transportConfig'

# Test field access
cnspec run ms365 --tenant-id <id> --client-id <id> --certificate-path <path> \
  -c 'ms365.exchangeonline.transportConfig["SmtpClientAuthenticationDisabled"]'
```

#### Debugging Tips
1. **Use `--verbose` flag** to see debug output
2. **Test PowerShell commands manually** first
3. **Check field name casing** - JSON field names must match exactly
4. **Verify authentication permissions** for the specific service
5. **Use dictionary access initially** to understand the data structure

### Advanced MS365 Resource Patterns

#### Cross-Service References
Link resources across different MS365 services:

```go
// In SharePoint site resource
func (s *mqlMs365SharepointonlineSite) owner() (*mqlMicrosoftUser, error) {
    // Get user from Microsoft Graph using site owner ID
    return lookupUserById(s.MqlRuntime, s.OwnerId.Data)
}
```

#### Computed Fields
Add derived information to resources:

```go
// In mailbox resource  
func (m *mqlMs365ExchangeonlineMailbox) isAuditingComplete() (bool, error) {
    // Logic to determine if all audit categories are enabled
    return len(m.AuditAdmin.Data) > 0 && len(m.AuditDelegate.Data) > 0 && len(m.AuditOwner.Data) > 0, nil
}
```

#### Resource Filtering
Use init functions for performance:

```go
func (u *mqlMicrosoftUsers) init(args *resources.Args) (*resources.Args, MicrosoftUsers, error) {
    if x, ok := (*args)["filter"]; ok {
        filter, ok := x.Value.(string)
        if !ok {
            return nil, nil, errors.New("filter value is not a string")
        }
        u.Filter = filter
    }
    return args, u, nil
}
```

This comprehensive guide provides the complete workflow for adding resources to the MS365 provider, using real implementation examples and proven patterns from the codebase.

## Advanced MS365 Resource Implementation Patterns

### PowerShell-Based Security and Compliance Resources

For MS365 resources that require Security & Compliance Center or other specialized PowerShell modules, follow this proven pattern:

#### 1. Resource Hierarchy Definition (.lr file)

Use hierarchical resource definitions with proper scoping:

```lr
// Parent service resource
ms365.exchangeonline {
  // Nested security resource
  securityAndCompliance() ms365.exchangeonline.securityAndCompliance
}

// Private nested resource for complex data structures
private ms365.exchangeonline.securityAndCompliance {
  // Return collections as dict arrays for flexibility
  dlpCompliancePolicies() []dict
  // Add other compliance-related methods
  complianceRules() []dict
}
```

**Key Patterns:**
- Use `private` keyword for nested resources that shouldn't be directly accessed
- Use `[]dict` return types for PowerShell-based data that has complex schemas
- Group related functionality under logical parent resources

#### 2. Multi-Scope PowerShell Integration

Define service-specific token scopes and PowerShell scripts:

```go
const (
    outlookScope    = "https://outlook.office.com/.default"
    complianceScope = "https://ps.compliance.protection.outlook.com/.default"
    // Add other service-specific scopes as needed
)

// Separate PowerShell script for each service/scope
var securityAndComplianceReport = `
$appId = '%s'
$organization = '%s'
$tenantId = '%s'
$complianceToken = '%s'

Install-Module -Name ExchangeOnlineManagement -Scope CurrentUser -Force
Import-Module ExchangeOnlineManagement
Connect-IPPSSession -AccessToken $complianceToken -AppID $appId -Organization $organization -ShowBanner:$false

# Collect data from compliance-specific cmdlets
$DlpCompliancePolicy = @(Get-DlpCompliancePolicy)

# Structure output for consistent JSON parsing
$securityAndCompliance = @{ DlpCompliancePolicy = $DlpCompliancePolicy }
ConvertTo-Json -Depth 4 $securityAndCompliance
`
```

**Key Patterns:**
- Define scope constants for different MS365 services
- Use separate PowerShell scripts for different authentication contexts
- Structure PowerShell output as hash tables for consistent JSON conversion
- Handle module installation and import within scripts

#### 3. Caching Architecture with Internal Structs

Implement efficient caching for expensive PowerShell operations:

```go
// Define internal struct for caching
type mqlMs365ExchangeonlineSecurityAndComplianceInternal struct {
    scReportLock sync.Mutex                    // Thread safety
    fetched      bool                          // Cache status
    fetchErr     error                         // Error state
    report       *SecurityAndComplianceReport // Cached data
}

// Corresponding report struct
type SecurityAndComplianceReport struct {
    DlpCompliancePolicy []interface{} `json:"DlpCompliancePolicy"`
    // Add other fields as needed
}

// Implement single-fetch pattern
func (r *mqlMs365ExchangeonlineSecurityAndCompliance) getSecurityAndComplianceReport() (*SecurityAndComplianceReport, error) {
    r.scReportLock.Lock()
    defer r.scReportLock.Unlock()

    // Return cached result if available
    if r.fetched {
        return r.report, r.fetchErr
    }

    r.fetched = true // Mark as fetched to prevent duplicate calls

    // Implement data fetching logic here...
    // Store result in r.report for caching
}
```

**Key Patterns:**
- Use mutex locks for thread-safe caching
- Implement single-fetch pattern to avoid duplicate expensive operations
- Cache both successful results and errors
- Define separate report structs for JSON unmarshaling

#### 4. Authentication and Token Management

Handle multiple authentication scopes properly:

```go
func (r *mqlResource) fetchData() error {
    // Get connection from parent resource
    parent, err := CreateResource(r.MqlRuntime, "ms365.exchangeonline", nil)
    if err != nil {
        return err
    }
    exchangeOnline := parent.(*mqlMs365Exchangeonline)
    conn := exchangeOnline.MqlRuntime.Connection.(*connection.Ms365Connection)

    // Reuse organization resolution from parent
    organization, err := exchangeOnline.getOrg()
    if organization == "" || err != nil {
        return errors.New("no organization provided")
    }

    // Get scope-specific token
    ctx := context.Background()
    token := conn.Token()
    scopedToken, err := token.GetToken(ctx, policy.TokenRequestOptions{
        Scopes: []string{complianceScope}, // Use appropriate scope
    })
    if err != nil {
        return err
    }

    // Execute PowerShell with proper error handling
    fmtScript := fmt.Sprintf(scriptTemplate, conn.ClientId(), organization, conn.TenantId(), scopedToken.Token)
    res, err := conn.CheckAndRunPowershellScript(fmtScript)
    if err != nil {
        return err
    }

    // Handle execution results...
}
```

**Key Patterns:**
- Reuse parent resource methods for common functionality (organization resolution)
- Use scope-specific token acquisition for different MS365 services
- Implement consistent error handling throughout the chain
- Use secure parameter injection for PowerShell scripts

#### 5. Resource Method Implementation

Create accessor methods that leverage caching:

```go
// Parent resource creates nested resource
func (r *mqlMs365Exchangeonline) securityAndCompliance() (*mqlMs365ExchangeonlineSecurityAndCompliance, error) {
    resource, err := CreateResource(r.MqlRuntime, "ms365.exchangeonline.securityAndCompliance", nil)
    if err != nil {
        return nil, err
    }
    return resource.(*mqlMs365ExchangeonlineSecurityAndCompliance), nil
}

// Nested resource provides data access
func (r *mqlMs365ExchangeonlineSecurityAndCompliance) dlpCompliancePolicies() ([]interface{}, error) {
    report, err := r.getSecurityAndComplianceReport()
    if err != nil {
        return nil, err
    }
    // Convert to dict slice for flexible MQL access
    return convert.JsonToDictSlice(report.DlpCompliancePolicy)
}
```

**Key Patterns:**
- Parent resources create child resources using `CreateResource`
- Child resources implement data access methods
- Use `convert.JsonToDictSlice` for flexible PowerShell data access
- Keep method implementations simple - delegate complex logic to helper functions

#### 6. Error Handling Best Practices

Implement consistent error handling:

```go
func (r *mqlResource) fetchData() (*ReportType, error) {
    errHandler := func(err error) (*ReportType, error) {
        r.fetchErr = err // Cache error state
        return nil, err
    }

    // Check PowerShell execution results
    if res.ExitStatus != 0 {
        data, _ := io.ReadAll(res.Stderr)
        return errHandler(fmt.Errorf("failed to generate report (exit code %d): %s", res.ExitStatus, string(data)))
    }

    // Handle JSON parsing
    if err := json.Unmarshal(data, report); err != nil {
        return errHandler(fmt.Errorf("failed to parse JSON: %v", err))
    }

    return report, nil
}
```

**Key Patterns:**
- Use error handler functions to ensure consistent error caching
- Provide detailed error context for debugging
- Handle both PowerShell execution and data parsing errors
- Cache error states to prevent retry loops

This advanced pattern enables robust implementation of PowerShell-based MS365 resources with proper authentication, caching, and error handling while maintaining the flexibility needed for diverse MS365 service APIs.
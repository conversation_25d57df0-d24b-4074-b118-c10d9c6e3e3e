# Copyright (c) Mondoo, Inc.
# SPDX-License-Identifier: BUSL-1.1

resources:
  microsoft:
    fields:
      applications: {}
      domains: {}
      enterpriseApplications: {}
      groups: {}
      identityAndAccess: {}
      organizations: {}
      roles: {}
      serviceprincipals: {}
      settings: {}
      tenantDomainName: {}
      users: {}
    min_mondoo_version: 9.0.0
  microsoft.adminConsentRequestPolicy:
    fields:
      isEnabled: {}
      notifyReviewers: {}
      remindersEnabled: {}
      requestDurationInDays: {}
      reviewers: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.application:
    fields:
      api: {}
      appId: {}
      appRoles: {}
      applicationTemplateId: {}
      certificates: {}
      certification: {}
      createdAt: {}
      createdDateTime: {}
      defaultRedirectUri: {}
      description: {}
      disabledByMicrosoftStatus: {}
      displayName: {}
      groupMembershipClaims: {}
      hasExpiredCredentials: {}
      id: {}
      identifierUris: {}
      info: {}
      isDeviceOnlyAuthSupported: {}
      isFallbackPublicClient: {}
      name: {}
      nativeAuthenticationApisEnabled: {}
      notes: {}
      optionalClaims: {}
      owners: {}
      parentalControlSettings: {}
      publicClient: {}
      publisherDomain: {}
      requestSignatureVerification: {}
      samlMetadataUrl: {}
      secrets: {}
      serviceManagementReference: {}
      servicePrincipal: {}
      servicePrincipalLockConfiguration: {}
      signInAudience: {}
      spa: {}
      tags: {}
      tokenEncryptionKeyId: {}
      web: {}
    min_mondoo_version: 9.0.0
  microsoft.application.permission:
    fields:
      appId: {}
      appName: {}
      description: {}
      id: {}
      name: {}
      status: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.application.role:
    fields:
      allowedMemberTypes: {}
      description: {}
      id: {}
      isEnabled: {}
      name: {}
      value: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.applications:
    fields:
      length: {}
      list: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess:
    fields:
      authenticationMethodsPolicy: {}
      namedLocations: {}
      policies: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.authenticationMethodConfiguration:
    fields:
      id: {}
      state: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.authenticationMethodsPolicy:
    fields:
      authenticationMethodConfigurations: {}
      description: {}
      displayName: {}
      id: {}
      lastModifiedDateTime: {}
      policyVersion: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.countryNamedLocation:
    fields:
      lookupMethod: {}
      name: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.ipNamedLocation:
    fields:
      name: {}
      trusted: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.namedLocations:
    fields:
      countryLocations: {}
      ipLocations: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy:
    fields:
      conditions: {}
      createdDateTime: {}
      displayName: {}
      grantControls: {}
      id: {}
      modifiedDateTime: {}
      sessionControls: {}
      state: {}
      templateId: {}
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions:
    fields:
      applications: {}
      authenticationFlows: {}
      clientAppTypes: {}
      clientApplications: {}
      id: {}
      insiderRiskLevels: {}
      locations: {}
      platforms: {}
      servicePrincipalRiskLevels: {}
      signInRiskLevels: {}
      userRiskLevels: {}
      users: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.applications:
    fields:
      excludeApplications: {}
      includeApplications: {}
      includeUserActions: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.authenticationFlows:
    fields:
      transferMethods: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.clientApplications:
    fields:
      excludeServicePrincipals: {}
      includeServicePrincipals: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.locations:
    fields:
      excludeLocations: {}
      includeLocations: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.platforms:
    fields:
      excludePlatforms: {}
      includePlatforms: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.conditions.users:
    fields:
      excludeGroups: {}
      excludeRoles: {}
      excludeUsers: {}
      includeGroups: {}
      includeRoles: {}
      includeUsers: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.grantControls:
    fields:
      authenticationStrength: {}
      builtInControls: {}
      customAuthenticationFactors: {}
      id: {}
      operator: {}
      termsOfUse: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.grantControls.authenticationStrength:
    fields:
      allowedCombinations: {}
      createdDateTime: {}
      description: {}
      displayName: {}
      id: {}
      modifiedDateTime: {}
      policyType: {}
      requirementsSatisfied: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.sessionControls:
    fields:
      applicationEnforcedRestrictions: {}
      cloudAppSecurity: {}
      id: {}
      persistentBrowser: {}
      secureSignInSession: {}
      signInFrequency: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.sessionControls.applicationEnforcedRestrictions:
    fields:
      isEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.sessionControls.cloudAppSecurity:
    fields:
      cloudAppSecurityType: {}
      isEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.sessionControls.persistentBrowser:
    fields:
      isEnabled: {}
      mode: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.conditionalAccess.policy.sessionControls.signInFrequency:
    fields:
      authenticationType: {}
      frequencyInterval: {}
      isEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.device:
    fields:
      deviceCategory: {}
      deviceId: {}
      displayName: {}
      enrollmentProfileName: {}
      enrollmentType: {}
      id: {}
      isCompliant: {}
      isManaged: {}
      isRooted: {}
      manufacturer: {}
      mdmAppId: {}
      model: {}
      operatingSystem: {}
      operatingSystemVersion: {}
      physicalIds: {}
      registrationDateTime: {}
      systemLabels: {}
      trustType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.devicemanagement:
    fields:
      deviceCompliancePolicies: {}
      deviceConfigurations: {}
      deviceEnrollmentConfigurations: {}
      managedDevices: {}
    min_mondoo_version: 9.0.0
  microsoft.devicemanagement.deviceEnrollmentConfiguration:
    fields:
      createdDateTime: {}
      description: {}
      displayName: {}
      id: {}
      lastModifiedDateTime: {}
      priority: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.devicemanagement.devicecompliancepolicy:
    fields:
      assignments: {}
      createdDateTime: {}
      description: {}
      displayName: {}
      id: {}
      lastModifiedDateTime: {}
      properties: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.devicemanagement.deviceconfiguration:
    fields:
      createdDateTime: {}
      description: {}
      displayName: {}
      id: {}
      lastModifiedDateTime: {}
      properties: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.devicemanagement.manageddevice:
    fields:
      androidSecurityPatchLevel: {}
      azureADRegistered: {}
      azureActiveDirectoryDeviceId: {}
      deviceCategoryDisplayName: {}
      easActivated: {}
      easDeviceId: {}
      emailAddress: {}
      enrollmentProfileName: {}
      ethernetMacAddress: {}
      iccid: {}
      id: {}
      imei: {}
      isEncrypted: {}
      isSupervised: {}
      jailBroken: {}
      manufacturer: {}
      meid: {}
      model: {}
      name: {}
      notes: {}
      operatingSystem: {}
      osVersion: {}
      serialNumber: {}
      udid: {}
      userDisplayName: {}
      userId: {}
      userPrincipalName: {}
      wiFiMacAddress: {}
      windowsProtectionState: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.devices:
    fields:
      filter: {}
      list: {}
      search: {}
    min_mondoo_version: 9.0.0
  microsoft.domain:
    fields:
      authenticationType: {}
      availabilityStatus: {}
      id: {}
      isAdminManaged: {}
      isDefault: {}
      isInitial: {}
      isRoot: {}
      isVerified: {}
      passwordNotificationWindowInDays: {}
      passwordValidityPeriodInDays: {}
      serviceConfigurationRecords: {}
      supportedServices: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.domaindnsrecord:
    fields:
      id: {}
      isOptional: {}
      label: {}
      properties: {}
      recordType: {}
      supportedService: {}
      ttl: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.graph.accessReviewReviewerScope:
    fields:
      query: {}
      queryRoot: {}
      queryType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.group:
    fields:
      displayName: {}
      groupTypes: {}
      id: {}
      mail: {}
      mailEnabled: {}
      mailNickname: {}
      members: {}
      membershipRule: {}
      membershipRuleProcessingState: {}
      securityEnabled: {}
      visibility: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.groups:
    fields:
      length: {}
      list: {}
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess:
    fields:
      filter: {}
      identityAndSignIn: {}
      list: {}
      roleEligibilityScheduleInstances: {}
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.identityAndSignIn:
    fields:
      policies: {}
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.identityAndSignIn.policies:
    fields:
      identitySecurityDefaultsEnforcementPolicy: {}
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.identityAndSignIn.policies.identitySecurityDefaultsEnforcementPolicy:
    fields:
      description: {}
      displayName: {}
      id: {}
      isEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.policy:
    fields:
      description: {}
      displayName: {}
      id: {}
      isOrganizationDefault: {}
      lastModifiedBy: {}
      lastModifiedDateTime: {}
      rules: {}
      scopeId: {}
      scopeType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.policy.rule:
    fields:
      id: {}
      target: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.policy.rule.target:
    fields:
      caller: {}
      enforcedSettings: {}
      inheritableSettings: {}
      level: {}
      operations: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.identityAndAccess.roleEligibilityScheduleInstance:
    fields:
      appScopeId: {}
      directoryScopeId: {}
      endDateTime: {}
      id: {}
      memberType: {}
      principalId: {}
      roleDefinitionId: {}
      roleEligibilityScheduleId: {}
      startDateTime: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.keyCredential:
    fields:
      description: {}
      expired: {}
      expires: {}
      keyId: {}
      thumbprint: {}
      type: {}
      usage: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.passwordCredential:
    fields:
      description: {}
      expired: {}
      expires: {}
      hint: {}
      keyId: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.policies:
    fields:
      adminConsentRequestPolicy: {}
      authenticationMethodsPolicy: {}
      authorizationPolicy: {}
      consentPolicySettings: {}
      identitySecurityDefaultsEnforcementPolicy: {}
      permissionGrantPolicies: {}
    min_mondoo_version: 9.0.0
  microsoft.policies.authenticationMethodConfiguration:
    fields:
      excludeTargets: {}
      id: {}
      includeTargets: {}
      state: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.policies.authenticationMethodsPolicy:
    fields:
      authenticationMethodConfigurations: {}
      description: {}
      displayName: {}
      id: {}
      lastModifiedDateTime: {}
      policyVersion: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.rolemanagement:
    fields:
      roleDefinitions: {}
    min_mondoo_version: 9.0.0
  microsoft.rolemanagement.roleassignment:
    fields:
      id: {}
      principal: {}
      principalId: {}
      roleDefinitionId: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.rolemanagement.roledefinition:
    fields:
      assignments: {}
      description: {}
      displayName: {}
      id: {}
      isBuiltIn: {}
      isEnabled: {}
      rolePermissions: {}
      templateId: {}
      version: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.roles:
    fields:
      filter: {}
      list: {}
      search: {}
    min_mondoo_version: 9.0.0
  microsoft.security:
    fields:
      latestSecureScores: {}
      riskyUsers: {}
      secureScores: {}
    min_mondoo_version: 9.0.0
  microsoft.security.riskyUser:
    fields:
      id: {}
      lastUpdatedAt: {}
      name: {}
      principalName: {}
      riskDetail: {}
      riskLevel: {}
      riskState: {}
      user: {}
    min_mondoo_version: 9.0.0
  microsoft.security.securityscore:
    fields:
      activeUserCount: {}
      averageComparativeScores: {}
      azureTenantId: {}
      controlScores: {}
      createdDateTime: {}
      currentScore: {}
      enabledServices: {}
      id: {}
      licensedUserCount: {}
      maxScore: {}
      vendorInformation: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.serviceprincipal:
    fields:
      accountEnabled: {}
      appId: {}
      appOwnerOrganizationId: {}
      appRoleAssignmentRequired: {}
      appRoles: {}
      applicationTemplateId: {}
      assignmentRequired: {}
      assignments: {}
      description: {}
      enabled: {}
      homepageUrl: {}
      id: {}
      isFirstParty: {}
      loginUrl: {}
      logoutUrl: {}
      name: {}
      notes: {}
      notificationEmailAddresses: {}
      permissions: {}
      preferredSingleSignOnMode: {}
      replyUrls: {}
      servicePrincipalNames: {}
      signInAudience: {}
      tags: {}
      termsOfServiceUrl: {}
      type: {}
      verifiedPublisher: {}
      visibleToUsers: {}
    min_mondoo_version: 9.0.0
  microsoft.serviceprincipal.assignment:
    fields:
      displayName: {}
      id: {}
      type: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.tenant:
    fields:
      assignedPlans: {}
      createdAt: {}
      createdDateTime: {}
      displayName: {}
      formsSettings: {}
      id: {}
      name: {}
      onPremisesSyncEnabled: {}
      preferredLanguage: {}
      privacyProfile: {}
      provisionedPlans: {}
      settings: {}
      subscriptions: {}
      technicalNotificationMails: {}
      type: {}
      verifiedDomains: {}
    min_mondoo_version: 9.0.0
  microsoft.tenant.formsSettings:
    fields:
      isBingImageSearchEnabled: {}
      isExternalSendFormEnabled: {}
      isExternalShareCollaborationEnabled: {}
      isExternalShareResultEnabled: {}
      isExternalShareTemplateEnabled: {}
      isInOrgFormsPhishingScanEnabled: {}
      isRecordIdentityByDefaultEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.tenant.settings:
    fields:
      isAppAndServicesTrialEnabled: {}
      isOfficeStoreEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user:
    fields:
      accountEnabled: {}
      assignedLicenses: {}
      auditlog: {}
      authMethods: {}
      authenticationRequirements: {}
      city: {}
      companyName: {}
      contact: {}
      country: {}
      createdDateTime: {}
      creationType: {}
      department: {}
      displayName: {}
      employeeId: {}
      givenName: {}
      id: {}
      identities: {}
      job: {}
      jobTitle: {}
      licenseDetails: {}
      mail: {}
      mfaEnabled: {}
      mobilePhone: {}
      officeLocation: {}
      otherMails: {}
      postalCode: {}
      settings: {}
      state: {}
      streetAddress: {}
      surname: {}
      userPrincipalName: {}
      userType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.assignedLicense:
    fields:
      disabledPlans: {}
      skuId: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.auditlog:
    fields:
      lastInteractiveSignIn: {}
      lastNonInteractiveSignIn: {}
      signins: {}
      userId: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.authenticationMethods:
    fields:
      count: {}
      emailMethods: {}
      fido2Methods: {}
      microsoftAuthenticator: {}
      passwordMethods: {}
      phoneMethods: {}
      registrationDetails: {}
      softwareMethods: {}
      temporaryAccessPassMethods: {}
      windowsHelloMethods: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.authenticationMethods.userRegistrationDetails:
    fields:
      id: {}
      isAdmin: {}
      isMfaCapable: {}
      isMfaRegistered: {}
      isPasswordlessCapable: {}
      isSsprCapable: {}
      isSsprEnabled: {}
      isSsprRegistered: {}
      isSystemPreferredAuthenticationMethodEnabled: {}
      lastUpdatedDateTime: {}
      methodsRegistered: {}
      systemPreferredAuthenticationMethods: {}
      userDisplayName: {}
      userPreferredMethodForSecondaryAuthentication: {}
      userPrincipalName: {}
      userType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.authenticationRequirements:
    fields:
      perUserMfaState: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.identity:
    fields:
      issuer: {}
      issuerAssignedId: {}
      signInType: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.licenseDetail:
    fields:
      id: {}
      servicePlans: {}
      skuId: {}
      skuPartNumber: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.licenseDetail.servicePlanInfo:
    fields:
      appliesTo: {}
      provisioningStatus: {}
      servicePlanId: {}
      servicePlanName: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.user.signin:
    fields:
      appDisplayName: {}
      clientAppUsed: {}
      createdDateTime: {}
      id: {}
      interactive: {}
      resourceDisplayName: {}
      userDisplayName: {}
      userId: {}
    is_private: true
    min_mondoo_version: 9.0.0
  microsoft.users:
    fields:
      filter: {}
      list: {}
      search: {}
    min_mondoo_version: 9.0.0
  ms365.exchangeonline:
    fields:
      adminAuditLogConfig: {}
      antiPhishPolicy: {}
      atpPolicyForO365: {}
      authenticationPolicy: {}
      dkimSigningConfig: {}
      externalInOutlook: {}
      hostedOutboundSpamFilterPolicy: {}
      mailbox: {}
      mailboxesWithAudit: {}
      malwareFilterPolicy: {}
      organizationConfig: {}
      owaMailboxPolicy: {}
      phishFilterPolicy: {}
      remoteDomain: {}
      reportSubmissionPolicies: {}
      roleAssignmentPolicy: {}
      safeAttachmentPolicy: {}
      safeLinksPolicy: {}
      securityAndCompliance: {}
      sharedMailboxes: {}
      sharingPolicy: {}
      teamsProtectionPolicies: {}
      transportConfig: {}
      transportRule: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.exoMailbox:
    fields:
      externalDirectoryObjectId: {}
      identity: {}
      user: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.externalSender:
    fields:
      allowList: {}
      enabled: {}
      identity: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.mailbox:
    fields:
      auditAdmin: {}
      auditDelegate: {}
      auditEnabled: {}
      auditLogAgeLimit: {}
      auditOwner: {}
      displayName: {}
      identity: {}
      primarySmtpAddress: {}
      recipientTypeDetails: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.reportSubmissionPolicy:
    fields:
      reportChatMessageEnabled: {}
      reportChatMessageToCustomizedAddressEnabled: {}
      reportJunkAddresses: {}
      reportJunkToCustomizedAddress: {}
      reportNotJunkAddresses: {}
      reportNotJunkToCustomizedAddress: {}
      reportPhishAddresses: {}
      reportPhishToCustomizedAddress: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.securityAndCompliance:
    fields:
      dlpCompliancePolicies: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.exchangeonline.teamsProtectionPolicy:
    fields:
      isValid: {}
      zapEnabled: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.sharepointonline:
    fields:
      defaultLinkPermission: {}
      spoSites: {}
      spoTenant: {}
      spoTenantSyncClientRestriction: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.sharepointonline.site:
    fields:
      denyAddAndCustomizePages: {}
      url: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.teams:
    fields:
      csTeamsClientConfiguration: {}
      csTeamsMeetingPolicy: {}
      csTeamsMessagingPolicy: {}
      csTenantFederationConfiguration: {}
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.teams.teamsMeetingPolicyConfig:
    fields:
      allowAnonymousUsersToJoinMeeting: {}
      allowAnonymousUsersToStartMeeting: {}
      allowCloudRecordingForCalls: {}
      allowExternalNonTrustedMeetingChat: {}
      allowExternalParticipantGiveRequestControl: {}
      allowPSTNUsersToBypassLobby: {}
      allowSecurityEndUserReporting: {}
      autoAdmittedUsers: {}
      designatedPresenterRoleMode: {}
      meetingChatEnabledType: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.teams.teamsMessagingPolicyConfig:
    fields:
      allowSecurityEndUserReporting: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
  ms365.teams.tenantFederationConfig:
    fields:
      allowFederatedUsers: {}
      allowPublicUsers: {}
      allowTeamsConsumer: {}
      allowTeamsConsumerInbound: {}
      allowedDomains: {}
      blockedDomains: {}
      identity: {}
      restrictTeamsConsumerToExternalUserProfiles: {}
      sharedSipAddressSpace: {}
      treatDiscoveredPartnersAsUnverified: {}
    is_private: true
    min_mondoo_version: 9.0.0
    platform:
      name:
      - microsoft365
